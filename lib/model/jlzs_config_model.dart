/// 巨量指数配置数据模型
class JlzsConfigModel {
  List<String> keywords;           // 关键词列表
  List<String> selectedRegions;    // 选中的地区
  DateTime startDate;              // 开始日期
  DateTime endDate;                // 结束日期
  String platform;                 // 平台 (aweme/toutiao)
  String regionType;               // 地区类型 (separate/merged)
  int extractInterval;             // 提取间隔(秒)
  List<String> processingPeriods;  // 处理周期 (day/week/month/year) - 支持多选
  String aggregationMethod;        // 聚合方式 (sum/average)
  String savePath;                 // 保存路径

  JlzsConfigModel({
    this.keywords = const [],
    this.selectedRegions = const [],
    DateTime? startDate,
    DateTime? endDate,
    this.platform = 'aweme',
    this.regionType = 'separate',
    this.extractInterval = 30,
    this.processingPeriods = const ['day'],
    this.aggregationMethod = 'sum',
    this.savePath = '',
  }) : startDate = startDate ?? DateTime.now().subtract(const Duration(days: 30)),
       endDate = endDate ?? DateTime.now();

  /// 从JSON创建实例
  factory JlzsConfigModel.fromJson(Map<String, dynamic> json) {
    return JlzsConfigModel(
      keywords: List<String>.from(json['keywords'] ?? []),
      selectedRegions: List<String>.from(json['selectedRegions'] ?? []),
      startDate: DateTime.parse(json['startDate'] ?? DateTime.now().subtract(const Duration(days: 30)).toIso8601String()),
      endDate: DateTime.parse(json['endDate'] ?? DateTime.now().toIso8601String()),
      platform: json['platform'] ?? 'aweme',
      regionType: json['regionType'] ?? 'separate',
      extractInterval: json['extractInterval'] ?? 30,
      processingPeriods: List<String>.from(json['processingPeriods'] ?? ['day']),
      aggregationMethod: json['aggregationMethod'] ?? 'sum',
      savePath: json['savePath'] ?? '',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'keywords': keywords,
      'selectedRegions': selectedRegions,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'platform': platform,
      'regionType': regionType,
      'extractInterval': extractInterval,
      'processingPeriods': processingPeriods,
      'aggregationMethod': aggregationMethod,
      'savePath': savePath,
    };
  }

  /// 复制实例
  JlzsConfigModel copyWith({
    List<String>? keywords,
    List<String>? selectedRegions,
    DateTime? startDate,
    DateTime? endDate,
    String? platform,
    String? regionType,
    int? extractInterval,
    List<String>? processingPeriods,
    String? aggregationMethod,
    String? savePath,
  }) {
    return JlzsConfigModel(
      keywords: keywords ?? this.keywords,
      selectedRegions: selectedRegions ?? this.selectedRegions,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      platform: platform ?? this.platform,
      regionType: regionType ?? this.regionType,
      extractInterval: extractInterval ?? this.extractInterval,
      processingPeriods: processingPeriods ?? this.processingPeriods,
      aggregationMethod: aggregationMethod ?? this.aggregationMethod,
      savePath: savePath ?? this.savePath,
    );
  }

  /// 验证配置是否有效
  bool isValid() {
    return keywords.isNotEmpty &&
           selectedRegions.isNotEmpty &&
           startDate.isBefore(endDate) &&
           platform.isNotEmpty &&
           regionType.isNotEmpty &&
           extractInterval > 0 &&
           processingPeriods.isNotEmpty &&
           aggregationMethod.isNotEmpty &&
           savePath.isNotEmpty;
  }

  /// 获取关键词数量
  int get keywordCount => keywords.length;

  /// 获取地区数量
  int get regionCount => selectedRegions.length;

  /// 获取时间范围天数
  int get dayRange => endDate.difference(startDate).inDays + 1;

  /// 获取时间范围显示文本
  String get dateRangeDisplay {
    final startStr = '${startDate.year}-${startDate.month.toString().padLeft(2, '0')}-${startDate.day.toString().padLeft(2, '0')}';
    final endStr = '${endDate.year}-${endDate.month.toString().padLeft(2, '0')}-${endDate.day.toString().padLeft(2, '0')}';
    return '$startStr 至 $endStr';
  }

  /// 获取平台显示名称
  String get platformDisplayName {
    switch (platform) {
      case 'aweme':
        return '抖音';
      case 'toutiao':
        return '头条';
      default:
        return platform;
    }
  }

  /// 获取地区类型显示名称
  String get regionTypeDisplayName {
    switch (regionType) {
      case 'separate':
        return '地区分开查询';
      case 'merged':
        return '地区合并查询';
      default:
        return regionType;
    }
  }

  /// 获取处理周期显示名称
  String get processingPeriodDisplayName {
    if (processingPeriods.isEmpty) return '未设置';

    final displayNames = processingPeriods.map((period) {
      switch (period) {
        case 'day':
          return '日';
        case 'week':
          return '周';
        case 'month':
          return '月';
        case 'year':
          return '年';
        default:
          return period;
      }
    }).toList();

    return displayNames.join('、');
  }

  /// 获取聚合方式显示名称
  String get aggregationMethodDisplayName {
    switch (aggregationMethod) {
      case 'sum':
        return '数据加总';
      case 'average':
        return '数据平均';
      default:
        return aggregationMethod;
    }
  }

  @override
  String toString() {
    return 'JlzsConfigModel{keywords: $keywords, selectedRegions: $selectedRegions, '
           'startDate: $startDate, endDate: $endDate, platform: $platform, '
           'regionType: $regionType, extractInterval: $extractInterval, '
           'processingPeriods: $processingPeriods, aggregationMethod: $aggregationMethod, '
           'savePath: $savePath}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is JlzsConfigModel &&
          runtimeType == other.runtimeType &&
          keywords.toString() == other.keywords.toString() &&
          selectedRegions.toString() == other.selectedRegions.toString() &&
          startDate == other.startDate &&
          endDate == other.endDate &&
          platform == other.platform &&
          regionType == other.regionType &&
          extractInterval == other.extractInterval &&
          processingPeriods.toString() == other.processingPeriods.toString() &&
          aggregationMethod == other.aggregationMethod &&
          savePath == other.savePath;

  @override
  int get hashCode =>
      keywords.hashCode ^
      selectedRegions.hashCode ^
      startDate.hashCode ^
      endDate.hashCode ^
      platform.hashCode ^
      regionType.hashCode ^
      extractInterval.hashCode ^
      processingPeriods.hashCode ^
      aggregationMethod.hashCode ^
      savePath.hashCode;
}

/// 树节点数据结构 (用于地区选择)
class TreeNode {
  final String name;
  final int id;
  bool isChecked;
  List<TreeNode> children;

  TreeNode(this.name, this.id, this.children, {this.isChecked = false});

  /// 递归设置所有子节点的选中状态
  void setChildrenChecked(bool checked) {
    isChecked = checked;
    for (var child in children) {
      child.setChildrenChecked(checked);
    }
  }

  /// 只设置子节点的选中状态（不包括自己）
  void setOnlyChildrenChecked(bool checked) {
    for (var child in children) {
      child.setChildrenChecked(checked);
    } 
  }

  /// 检查是否有子节点被选中
  bool hasCheckedChildren() {
    for (var child in children) {
      if (child.isChecked || child.hasCheckedChildren()) {
        return true;
      }
    }
    return false;
  }

  /// 获取所有被选中的节点名称
  List<String> getCheckedNames() {
    List<String> result = [];
    if (isChecked) {
      result.add(name);
    }
    for (var child in children) {
      result.addAll(child.getCheckedNames());
    }
    return result;
  }

  /// 检查是否所有子节点都被选中
  bool areAllChildrenChecked() {
    if (children.isEmpty) return false;
    return children.every((child) => child.isChecked);
  }

  /// 检查是否有部分子节点被选中
  bool areSomeChildrenChecked() {
    if (children.isEmpty) return false;
    return children.any((child) => child.isChecked) && !areAllChildrenChecked();
  }

  /// 根据名称查找并选中节点
  bool selectByName(String targetName) {
    if (name == targetName) {
      isChecked = true;
      return true;
    }
    for (var child in children) {
      if (child.selectByName(targetName)) {
        return true;
      }
    }
    return false;
  }
}

/// 关键词转换规则
class KeywordConversionRules {
  final bool toLowerCase;      // 转换为小写
  final bool addPrefix;        // 添加前缀
  final String prefix;         // 前缀内容
  final bool addSuffix;        // 添加后缀
  final String suffix;         // 后缀内容

  const KeywordConversionRules({
    this.toLowerCase = false,
    this.addPrefix = false,
    this.prefix = '',
    this.addSuffix = false,
    this.suffix = '',
  });

  KeywordConversionRules copyWith({
    bool? toLowerCase,
    bool? addPrefix,
    String? prefix,
    bool? addSuffix,
    String? suffix,
  }) {
    return KeywordConversionRules(
      toLowerCase: toLowerCase ?? this.toLowerCase,
      addPrefix: addPrefix ?? this.addPrefix,
      prefix: prefix ?? this.prefix,
      addSuffix: addSuffix ?? this.addSuffix,
      suffix: suffix ?? this.suffix,
    );
  }

  /// 应用转换规则到关键词
  String applyTo(String keyword) {
    String result = keyword.trim();

    if (result.isEmpty) return result;

    // 转换为小写
    if (toLowerCase) {
      result = result.toLowerCase();
    }

    // 添加前缀
    if (addPrefix && prefix.isNotEmpty) {
      result = prefix + result;
    }

    // 添加后缀
    if (addSuffix && suffix.isNotEmpty) {
      result = result + suffix;
    }

    return result;
  }

  /// 批量应用转换规则
  List<String> applyToList(List<String> keywords) {
    return keywords.map((keyword) => applyTo(keyword)).toList();
  }
}
