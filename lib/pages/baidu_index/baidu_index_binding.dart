import 'package:get/get.dart';

import 'baidu_index_logic.dart';
import 'services/baidu_log_service.dart';
import 'services/baidu_area_service.dart';
import 'services/baidu_api_service.dart';
import 'services/baidu_data_service.dart';
import 'services/baidu_account_service.dart';
import 'services/baidu_proxy_service.dart';
import 'services/baidu_task_service.dart';
import 'services/baidu_util_service.dart';

class BaiduIndexBinding extends Bindings {
  @override
  void dependencies() {
    // 按依赖顺序注册服务
    // 1. 基础服务（无依赖）
    Get.lazyPut<BaiduLogService>(() => BaiduLogService());
    Get.lazyPut<BaiduUtilService>(() => BaiduUtilService());

    // 2. 数据和API服务（依赖基础服务）
    Get.lazyPut<BaiduAreaService>(() => BaiduAreaService());
    Get.lazyPut<BaiduApiService>(() => BaiduApiService());
    Get.lazyPut<BaiduDataService>(() => BaiduDataService());

    // 3. 业务服务（依赖前面的服务）
    Get.lazyPut<BaiduAccountService>(() => BaiduAccountService());
    Get.lazyPut<BaiduProxyService>(() => BaiduProxyService());

    // 4. 任务服务（依赖所有其他服务）
    Get.lazyPut<BaiduTaskService>(() => BaiduTaskService());

    // 5. 主控制器（依赖所有服务）
    Get.lazyPut(() => BaiduIndexLogic());
  }
}
