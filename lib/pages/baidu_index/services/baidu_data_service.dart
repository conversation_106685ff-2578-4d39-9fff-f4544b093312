import 'dart:convert';
import 'dart:io';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:csv/csv.dart';
import 'package:excel/excel.dart';
import '../../../model/data_model.dart';

/// 百度指数数据处理服务
/// 职责：数据解密、转换、导出、格式化
class BaiduDataService extends GetxService {
  
  /// 解密数据
  /// [ptbk] 解密密钥
  /// [encryptedData] 加密数据
  String decrypt(String ptbk, String encryptedData) {
    if (ptbk.isEmpty || encryptedData.isEmpty) {
      return "";
    }
    
    int n = ptbk.length ~/ 2;
    Map<String, String> d = {
      for (int o = 0; o < n; o++) ptbk[o]: ptbk[n + o]
    };
    
    List<String> decryptedData = encryptedData
        .split('')
        .map((data) => d[data] ?? "")
        .toList();
    
    return decryptedData.join("");
  }
  
  /// 数据填充零（处理空数据）
  /// [data] 要处理的数据字符串
  int fillZero(String data) {
    return data.isEmpty ? 0 : int.parse(data);
  }
  
  /// 生成零数组
  /// [length] 数组长度
  List<int> generateZeroArray(int length) {
    return List.generate(length, (_) => 0);
  }
  
  /// 解密并转换数据为整数列表
  /// [ptbk] 解密密钥
  /// [encryptedData] 加密数据
  /// [expectedLength] 期望的数据长度
  List<int> decryptToIntList(String ptbk, String encryptedData, int expectedLength) {
    if (encryptedData.isEmpty) {
      return generateZeroArray(expectedLength);
    }
    
    final decryptedString = decrypt(ptbk, encryptedData);
    final decryptedList = decryptedString
        .split(',')
        .map((data) => fillZero(data))
        .toList();
    
    // 如果解密后的数据长度不足，用零填充
    if (decryptedList.length < expectedLength) {
      return generateZeroArray(expectedLength);
    }
    
    return decryptedList;
  }
  
  /// 生成日期列表
  /// [startDate] 开始日期 (yyyy-MM-dd)
  /// [endDate] 结束日期 (yyyy-MM-dd)
  List<String> generateDateList(String startDate, String endDate) {
    final start = DateTime.parse(startDate);
    final end = DateTime.parse(endDate);
    final daysDifference = end.difference(start).inDays + 1;
    
    return List<DateTime>.generate(
      daysDifference,
      (index) => start.add(Duration(days: index)),
    ).map((timestamp) => DateFormat('yyyy-MM-dd').format(timestamp)).toList();
  }
  
  /// 处理搜索指数数据
  /// [userIndexesData] 用户指数数据
  /// [ptbk] 解密密钥
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  Map<String, dynamic> processSearchIndexData(
    Map<String, dynamic> userIndexesData,
    String ptbk,
    String startDate,
    String endDate,
  ) {
    final word = (userIndexesData['word'] as List)
        .map((item) => item['name'])
        .join('+');
    
    final dateList = generateDateList(startDate, endDate);
    final expectedLength = dateList.length;
    
    final encryptedDataAll = userIndexesData['all']['data'] ?? '';
    final encryptedDataPc = userIndexesData['pc']['data'] ?? '';
    final encryptedDataWise = userIndexesData['wise']['data'] ?? '';
    
    // 处理数据为空的情况
    String finalAllData = encryptedDataAll;
    if (encryptedDataWise.isEmpty) {
      finalAllData = encryptedDataPc;
    }
    if (encryptedDataPc.isEmpty) {
      finalAllData = encryptedDataWise;
    }
    if (encryptedDataAll.isEmpty && encryptedDataPc.isEmpty && encryptedDataWise.isEmpty) {
      finalAllData = '';
    }
    
    return {
      'word': word,
      'all': decryptToIntList(ptbk, finalAllData, expectedLength),
      'pc': decryptToIntList(ptbk, encryptedDataPc, expectedLength),
      'wise': decryptToIntList(ptbk, encryptedDataWise, expectedLength),
      'dateList': dateList,
    };
  }
  
  /// 处理信息流指数数据
  /// [indexData] 指数数据
  /// [ptbk] 解密密钥
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  Map<String, dynamic> processFeedIndexData(
    Map<String, dynamic> indexData,
    String ptbk,
    String startDate,
    String endDate,
  ) {
    final word = (indexData['key'] as List)
        .map((item) => item['name'])
        .join('+');
    
    final dateList = generateDateList(startDate, endDate);
    final expectedLength = dateList.length;
    
    final encryptedDataAll = indexData['data'] ?? '';
    
    return {
      'word': word,
      'all': decryptToIntList(ptbk, encryptedDataAll, expectedLength),
      'pc': generateZeroArray(expectedLength),
      'wise': generateZeroArray(expectedLength),
      'dateList': dateList,
    };
  }
  
  /// 处理PPC品牌指数数据
  /// [ppcData] PPC数据列表
  List<int> processPpcIndexData(List<dynamic> ppcData) {
    return ppcData.map((item) => item['value'] as int).toList();
  }
  
  /// 导出数据为CSV格式
  /// [data] 要导出的数据
  /// [keyword] 关键词
  /// [region] 地区
  /// [filePath] 文件路径
  Future<void> exportToCSV({
    required List<DataModel> data,
    required String keyword,
    required String region,
    required String filePath,
  }) async {
    try {
      final csvData = <List<dynamic>>[];
      
      // 添加标题行
      csvData.add(['日期', '地区', '关键词', '综合指数', 'PC指数', '移动指数']);
      
      // 处理数据
      for (final dataModel in data) {
        if (dataModel.region != region) continue;
        
        dataModel.data?.forEach((year, dramaList) {
          for (final drama in dramaList) {
            if (drama.keyword != keyword) continue;
            
            final dateList = generateDateList(drama.startData!, drama.endData!);
            
            for (int i = 0; i < dateList.length; i++) {
              final allValue = i < (drama.all?.length ?? 0) ? drama.all![i] : 0;
              final pcValue = i < (drama.pc?.length ?? 0) ? drama.pc![i] : 0;
              final wiseValue = i < (drama.wise?.length ?? 0) ? drama.wise![i] : 0;
              
              csvData.add([
                dateList[i],
                region,
                keyword,
                allValue,
                pcValue,
                wiseValue,
              ]);
            }
          }
        });
      }
      
      // 写入CSV文件
      final csvString = const ListToCsvConverter().convert(csvData);
      final file = File(filePath);
      await file.writeAsString(csvString, encoding: utf8);
      
    } catch (e) {
      throw Exception('导出CSV失败: $e');
    }
  }
  
  /// 读取Excel文件的第一列数据
  /// [filePath] Excel文件路径
  Future<List<dynamic>> readExcelFile(String filePath) async {
    List<dynamic> firstColumnData = [];

    try {
      var bytes = File(filePath).readAsBytesSync();
      var excel = Excel.decodeBytes(bytes);

      // 输出Excel文件内容
      for (var table in excel.tables.keys) {
        var sheet = excel.tables[table]!;
        // 使用 sheet.rows 获取行数据并进行迭代处理
        for (var row in sheet.rows) {
          var value = row.isNotEmpty ? row.first?.value : '空行';
          firstColumnData.add(value); // 添加每一行的第一列数据到数组中
        }
      }
    } catch (e) {
      throw Exception('读取Excel文件失败：$e');
    }

    return firstColumnData;
  }
  
  /// 验证数据完整性
  /// [data] 要验证的数据
  bool validateDataIntegrity(List<DataModel> data) {
    for (final dataModel in data) {
      if (dataModel.region == null || dataModel.region!.isEmpty) {
        return false;
      }
      
      if (dataModel.data == null) continue;
      
      for (final dramaList in dataModel.data!.values) {
        for (final drama in dramaList) {
          if (drama.keyword == null || drama.keyword!.isEmpty) {
            return false;
          }
          if (drama.startData == null || drama.endData == null) {
            return false;
          }
        }
      }
    }
    
    return true;
  }
}
