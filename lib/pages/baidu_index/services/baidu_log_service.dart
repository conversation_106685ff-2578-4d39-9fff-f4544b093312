import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 百度指数日志管理服务
/// 职责：日志记录、清理、滚动控制
class BaiduLogService extends GetxService {
  // 日志列表，使用响应式变量
  final RxList<String> _logs = <String>[].obs;
  
  // 日志滚动控制器
  final ScrollController _logScrollController = ScrollController();
  
  // 最大日志条数
  static const int _maxLogCount = 500;
  
  /// 获取日志列表（只读）
  List<String> get logs => _logs.toList();
  
  /// 获取日志数量
  int get logCount => _logs.length;
  
  /// 获取日志滚动控制器
  ScrollController get logScrollController => _logScrollController;
  
  /// 添加日志
  /// [message] 日志消息内容
  void addLog(String message) {
    // 在开头插入新日志，包含时间戳
    final timestampedMessage = "[${DateTime.now().toString().split('.')[0]}] $message";
    _logs.insert(0, timestampedMessage);
    
    // 如果超过最大条数，删除最老的日志（列表末尾的）
    if (_logs.length > _maxLogCount) {
      _logs.removeRange(_maxLogCount, _logs.length);
    }
    
    // 优化滚动逻辑 - 滚动到顶部显示最新日志
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_logScrollController.hasClients) {
        _logScrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }
  
  /// 批量添加日志
  /// [messages] 日志消息列表
  void addLogs(List<String> messages) {
    for (final message in messages) {
      addLog(message);
    }
  }
  
  /// 清空所有日志
  void clearLogs() {
    _logs.clear();
  }
  
  /// 获取最近的N条日志
  /// [count] 要获取的日志条数
  List<String> getRecentLogs(int count) {
    if (count <= 0) return [];
    return _logs.take(count).toList();
  }
  
  /// 搜索包含关键词的日志
  /// [keyword] 搜索关键词
  List<String> searchLogs(String keyword) {
    if (keyword.isEmpty) return logs;
    return _logs.where((log) => log.toLowerCase().contains(keyword.toLowerCase())).toList();
  }
  
  /// 导出日志到字符串
  /// [separator] 日志之间的分隔符，默认为换行符
  String exportLogsAsString({String separator = '\n'}) {
    return _logs.reversed.join(separator); // 按时间正序导出
  }
  
  /// 获取日志统计信息
  Map<String, int> getLogStatistics() {
    int errorCount = 0;
    int warningCount = 0;
    int infoCount = 0;
    
    for (final log in _logs) {
      if (log.contains('❌') || log.contains('错误')) {
        errorCount++;
      } else if (log.contains('⚠️') || log.contains('警告')) {
        warningCount++;
      } else {
        infoCount++;
      }
    }
    
    return {
      'total': _logs.length,
      'error': errorCount,
      'warning': warningCount,
      'info': infoCount,
    };
  }
  
  @override
  void onClose() {
    _logScrollController.dispose();
    super.onClose();
  }
}
