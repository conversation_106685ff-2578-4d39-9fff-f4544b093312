import 'dart:async';
import 'dart:io';
import 'package:get/get.dart';
import '../../../model/baidu_user_model.dart';
import '../../../utils/http_client.dart';
import '../../../utils/toast_util.dart';
import 'baidu_log_service.dart';

/// 代理配置模型
class ProxyConfig {
  final String ip;
  final String port;
  final String username;
  final String password;
  
  ProxyConfig({
    required this.ip,
    required this.port,
    required this.username,
    required this.password,
  });
  
  /// 从字符串解析代理配置
  static ProxyConfig? fromString(String text) {
    final parts = text.trim().split(':');
    if (parts.length >= 2) {
      // 处理格式: ip:port 或 username:password@ip:port
      if (parts.length == 2) {
        return ProxyConfig(
          ip: parts[0],
          port: parts[1],
          username: '',
          password: '',
        );
      } else if (parts.length == 4) {
        // username:password@ip:port 格式
        final userPass = parts[0];
        final password = parts[1].split('@')[0];
        final ip = parts[1].split('@')[1];
        final port = parts[2];
        
        return ProxyConfig(
          ip: ip,
          port: port,
          username: userPass,
          password: password,
        );
      }
    }
    return null;
  }
}

/// 百度指数代理管理服务
/// 职责：代理检测、自动续期、配置管理、定时检查
class BaiduProxyService extends GetxService {
  // 注入服务
  final BaiduLogService _logService = Get.find<BaiduLogService>();
  final HttpClientUtil _httpClient = HttpClientUtil();
  
  // 代理检查定时器
  Timer? _proxyCheckTimer;
  
  // 代理配置
  String _proxyUrl = "";
  bool _isZDProxy = false;
  
  /// 设置代理URL
  void setProxyUrl(String url) {
    _proxyUrl = url;
  }
  
  /// 设置是否为自动代理
  void setZDProxy(bool isZD) {
    _isZDProxy = isZD;
  }
  
  /// 开始代理检查定时器
  void startProxyCheckTimer(List<BaiDuUsers> users) {
    _proxyCheckTimer?.cancel();
    
    // 每秒检查一次代理状态和更新UI
    _proxyCheckTimer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      final now = DateTime.now();
      
      for (int i = 0; i < users.length; i++) {
        var user = users[i];
        if (user.isProxy && user.proxyStartTime != null) {
          // 检查代理是否过期
          if (user.proxyValidTime != null) {
            final expiryTime = user.proxyStartTime!.add(Duration(minutes: user.proxyValidTime!));
            if (now.isAfter(expiryTime)) {
              user.isProxy = false;
              user.isStart = false;
              _logService.addLog("× 错误-账号:${user.username}---代理IP已过期");
              showToast("账号${user.username}的代理已过期，已自动关闭");
              
              if (_isZDProxy) {
                await autoRenewProxyForUser(user);
              }
            }
          }
        }
      }
    });
  }
  
  /// 停止代理检查定时器
  void stopProxyCheckTimer() {
    _proxyCheckTimer?.cancel();
    _proxyCheckTimer = null;
  }
  
  /// 自动续期代理
  /// [user] 要续期的用户
  Future<void> autoRenewProxyForUser(BaiDuUsers user) async {
    try {
      _logService.addLog("🚀 开始代理续期 - 账号: ${user.username}");
      
      // 发起代理请求
      final response = await _httpClient.get(
        url: _proxyUrl,
      ).catchError((e) {
        throw Exception('代理API请求失败: $e');
      });
      
      // 解析并验证代理
      final proxy = _parseProxyResponse(response.toString());
      _validateProxyConfiguration(proxy);
      
      // 更新用户数据
      _updateUserProxyInfo(user, proxy);
      
      _logService.addLog("✅ 成功续期 - 账号: ${user.username} 代理: ${proxy.ip}:${proxy.port}");
    } catch (e) {
      _logService.addLog("❌ 续期失败 - 账号: ${user.username} 原因: ${e.toString()}");
      rethrow;
    }
  }
  
  /// 解析代理响应
  ProxyConfig _parseProxyResponse(String response) {
    final configs = response.split('\n')
        .map((line) => line.trim())
        .where((line) => line.isNotEmpty)
        .map(ProxyConfig.fromString)
        .whereType<ProxyConfig>()
        .toList();
    
    if (configs.isEmpty) throw Exception('无有效代理配置');
    return configs.first;
  }
  
  /// 代理有效性验证
  void _validateProxyConfiguration(ProxyConfig proxy) {
    final ipValid = RegExp(r'^(\d{1,3}\.){3}\d{1,3}$').hasMatch(proxy.ip);
    final port = int.tryParse(proxy.port) ?? 0;
    
    if (!ipValid) throw Exception('无效IP格式: ${proxy.ip}');
    if (port <= 0 || port > 65535) throw Exception('无效端口: ${proxy.port}');
  }
  
  /// 更新用户代理信息
  void _updateUserProxyInfo(BaiDuUsers user, ProxyConfig proxy) {
    user
      ..proxyAddress = proxy.ip
      ..proxyPort = proxy.port
      ..proxyUsername = proxy.username
      ..proxyPassword = proxy.password
      ..proxyStartTime = DateTime.now()
      ..isProxy = true
      ..isStart = true;
  }
  
  /// 检查代理连接
  /// [proxyAddress] 代理地址
  /// [proxyPort] 代理端口
  /// [username] 用户名（可选）
  /// [password] 密码（可选）
  Future<bool> checkProxy(String proxyAddress, String proxyPort, [String? username, String? password]) async {
    try {
      final HttpClient client = HttpClient();
      
      // 设置代理
      client.findProxy = (uri) {
        if (username != null && username.isNotEmpty) {
          return 'PROXY $username:$password@$proxyAddress:$proxyPort';
        }
        return 'PROXY $proxyAddress:$proxyPort';
      };
      
      // 设置超时
      client.connectionTimeout = Duration(seconds: 10);
      
      // 测试连接（使用百度作为测试网站）
      final request = await client.getUrl(Uri.parse('https://www.baidu.com'));
      final response = await request.close();
      
      // 关闭客户端
      client.close();
      
      // 检查响应状态
      return response.statusCode == 200;
    } catch (e) {
      _logService.addLog('代理检测错误: $e');
      _logService.addLog("× 代理检测错误:$e");
      return false;
    }
  }
  
  /// 批量导入代理
  /// [proxies] 代理配置列表
  /// [validTime] 有效时间（分钟）
  /// [users] 用户列表
  Future<void> importProxies(List<ProxyConfig> proxies, int validTime, List<BaiDuUsers> users) async {
    int successCount = 0;
    int failCount = 0;
    
    _logService.addLog("🚀 开始批量导入代理，共${proxies.length}个");
    
    for (int i = 0; i < proxies.length && i < users.length; i++) {
      var user = users[i];
      
      if (!user.isStart) {  // 只给未启用的账号设置代理
        bool isValid = await checkProxy(
          proxies[i].ip,
          proxies[i].port,
          proxies[i].username,
          proxies[i].password,
        );
        
        if (isValid) {
          user.proxyAddress = proxies[i].ip;
          user.proxyPort = proxies[i].port;
          user.proxyUsername = proxies[i].username;
          user.proxyPassword = proxies[i].password;
          user.proxyValidTime = validTime;
          user.proxyStartTime = DateTime.now();
          user.isProxy = true;
          
          successCount++;
          _logService.addLog("✅ 账号${user.username}代理设置成功: ${proxies[i].ip}:${proxies[i].port}");
        } else {
          failCount++;
          _logService.addLog("❌ 账号${user.username}代理验证失败: ${proxies[i].ip}:${proxies[i].port}");
        }
      }
    }
    
    _logService.addLog("📊 批量导入完成 - 成功:$successCount, 失败:$failCount");
    showToast("代理导入完成：成功$successCount个，失败$failCount个");
  }
  
  /// 清除用户代理配置
  /// [user] 要清除代理的用户
  void clearUserProxy(BaiDuUsers user) {
    user.proxyAddress = null;
    user.proxyPort = null;
    user.proxyUsername = null;
    user.proxyPassword = null;
    user.proxyValidTime = null;
    user.proxyStartTime = null;
    user.isProxy = false;
    
    _logService.addLog("🧹 已清除账号" + user.username + "的代理配置");
  }
  
  /// 获取代理状态信息
  /// [user] 用户
  Map<String, dynamic> getProxyStatus(BaiDuUsers user) {
    if (!user.isProxy || user.proxyStartTime == null) {
      return {
        'status': 'inactive',
        'message': '未启用代理',
      };
    }
    
    if (user.proxyValidTime != null) {
      final now = DateTime.now();
      final expiryTime = user.proxyStartTime!.add(Duration(minutes: user.proxyValidTime!));
      final remainingMinutes = expiryTime.difference(now).inMinutes;
      
      if (remainingMinutes <= 0) {
        return {
          'status': 'expired',
          'message': '代理已过期',
        };
      } else {
        return {
          'status': 'active',
          'message': '剩余${remainingMinutes}分钟',
          'remainingMinutes': remainingMinutes,
        };
      }
    }
    
    return {
      'status': 'active',
      'message': '代理正常',
    };
  }
  
  @override
  void onClose() {
    stopProxyCheckTimer();
    super.onClose();
  }
}
