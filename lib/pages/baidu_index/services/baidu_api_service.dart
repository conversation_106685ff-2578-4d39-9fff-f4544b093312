import 'dart:convert';
import 'package:get/get.dart';
import '../../../utils/http_client.dart';
import '../../../model/baidu_user_model.dart';
import '../../../utils/encrypt.dart';

/// 百度指数API通信服务
/// 职责：统一的API请求处理、请求头构建、响应处理
class BaiduApiService extends GetxService {
  // HTTP客户端工具
  final HttpClientUtil _httpClient = HttpClientUtil();
  
  // API端点常量
  static const String _searchApi = "https://index.baidu.com/api/SearchApi/index";
  static const String _feedSearchApi = "https://index.baidu.com/api/FeedSearchApi/getFeedIndex";
  static const String _ppcApi = "https://index.baidu.com/insight/brand/queryBrandIndex";
  static const String _wordSugApi = "https://index.baidu.com/insight/word/sug";
  static const String _ptbkApi = "https://index.baidu.com/Interface/ptbk";
  
  /// 构建通用请求头
  /// [user] 用户信息
  /// [cipherText] 加密文本（可选）
  Map<String, String> buildHeaders(BaiDuUsers user, [String? cipherText]) {
    final headers = <String, String>{
      'Accept': 'application/json, text/plain, */*',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'close',
      'Host': 'index.baidu.com',
      'Referer': 'https://index.baidu.com/v2/main/index.html',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.438.400 QQBrowser/13.0.6071.400',
      'Cookie': user.cookie,
    };
    
    if (cipherText != null && cipherText.isNotEmpty) {
      headers['Cipher-Text'] = cipherText;
    }
    
    return headers;
  }
  
  /// 搜索API请求
  /// [params] 请求参数
  /// [user] 用户信息
  /// [cipherTextKeywords] 用于生成加密文本的关键词
  Future<Map<String, dynamic>> searchApiRequest({
    required Map<String, dynamic> params,
    required BaiDuUsers user,
    required List<String> cipherTextKeywords,
  }) async {
    try {
      final cipherText = getCipherText(cipherTextKeywords, user.apiKeyTime, user.apiKey);
      final headers = buildHeaders(user, cipherText);
      
      final response = await _httpClient.get<Map<String, dynamic>>(
        url: _searchApi,
        headers: headers,
        queryParams: params,
        maxRetries: 3,
        proxyAddress: user.isProxy ? '${user.proxyAddress}:${user.proxyPort}' : null,
      );
      
      return _handleApiResponse(response);
    } catch (e) {
      throw Exception('搜索API请求失败: $e');
    }
  }
  
  /// 信息流搜索API请求
  /// [params] 请求参数
  /// [user] 用户信息
  /// [cipherTextKeywords] 用于生成加密文本的关键词
  Future<Map<String, dynamic>> feedSearchApiRequest({
    required Map<String, dynamic> params,
    required BaiDuUsers user,
    required List<String> cipherTextKeywords,
  }) async {
    try {
      final cipherText = getCipherText(cipherTextKeywords, user.apiKeyTime, user.apiKey);
      final headers = buildHeaders(user, cipherText);
      
      final response = await _httpClient.get<Map<String, dynamic>>(
        url: _feedSearchApi,
        headers: headers,
        queryParams: params,
        maxRetries: 3,
        proxyAddress: user.isProxy ? '${user.proxyAddress}:${user.proxyPort}' : null,
      );
      
      return _handleApiResponse(response);
    } catch (e) {
      throw Exception('信息流搜索API请求失败: $e');
    }
  }
  
  /// PPC品牌指数API请求
  /// [params] 请求参数
  /// [user] 用户信息
  Future<Map<String, dynamic>> ppcApiRequest({
    required Map<String, dynamic> params,
    required BaiDuUsers user,
  }) async {
    try {
      final headers = buildHeaders(user);
      
      final response = await _httpClient.post<Map<String, dynamic>>(
        url: _ppcApi,
        headers: headers,
        data: params,
        maxRetries: 3,
        proxyAddress: user.isProxy ? '${user.proxyAddress}:${user.proxyPort}' : null,
      );
      
      return _handleApiResponse(response);
    } catch (e) {
      throw Exception('PPC API请求失败: $e');
    }
  }
  
  /// 词汇建议API请求
  /// [keywords] 关键词列表
  /// [user] 用户信息
  Future<Map<String, dynamic>> wordSugApiRequest({
    required List<String> keywords,
    required BaiDuUsers user,
  }) async {
    try {
      final headers = buildHeaders(user);
      final data = {
        "words": keywords,
        "source": "pc_home"
      };
      
      final response = await _httpClient.post<Map<String, dynamic>>(
        url: _wordSugApi,
        headers: headers,
        data: data,
        maxRetries: 3,
        proxyAddress: user.isProxy ? '${user.proxyAddress}:${user.proxyPort}' : null,
      );
      
      return _handleApiResponse(response);
    } catch (e) {
      throw Exception('词汇建议API请求失败: $e');
    }
  }
  
  /// 获取解密密钥API请求
  /// [uniqid] 唯一标识符
  /// [user] 用户信息
  Future<String> getPtbkApiRequest({
    required String uniqid,
    required BaiDuUsers user,
  }) async {
    try {
      // 检查缓存
      if (user.decryptCache.containsKey(uniqid)) {
        return user.decryptCache[uniqid]!;
      }
      
      final headers = buildHeaders(user);
      final ptbkUrl = '$_ptbkApi?uniqid=$uniqid';
      
      final response = await _httpClient.get<Map<String, dynamic>>(
        url: ptbkUrl,
        queryParams: {},
        headers: headers,
        proxyAddress: user.isProxy ? '${user.proxyAddress}:${user.proxyPort}' : null,
      );
      
      final result = _handleApiResponse(response);
      final ptbk = result['data'] as String;
      
      // 缓存结果
      user.decryptCache[uniqid] = ptbk;
      
      return ptbk;
    } catch (e) {
      throw Exception('获取解密密钥失败: $e');
    }
  }
  
  /// 处理API响应
  /// [response] API响应数据
  Map<String, dynamic> _handleApiResponse(Map<String, dynamic>? response) {
    if (response == null) {
      throw Exception('API响应为空');
    }
    
    final status = response['status'];
    final message = response['message'];
    
    // 检查状态码
    if (status.toString() == '10001') {
      throw Exception(message ?? '请求被阻止');
    }
    
    if (status.toString() != '0') {
      throw Exception(message ?? '请求失败');
    }
    
    return response;
  }
  
  /// 检查用户API状态
  /// [user] 用户信息
  bool isUserApiValid(BaiDuUsers user) {
    return user.cookie.isNotEmpty && 
           user.apiKey.isNotEmpty && 
           user.apiKeyTime.isNotEmpty;
  }
  
  /// 构建关键词参数
  /// [keywords] 关键词列表
  List<List<String>> buildKeywordParams(List<String> keywords) {
    return keywords.map((keyword) {
      if (keyword.contains('+')) {
        // 如果包含 "+"，按 "+" 分割并为每个部分生成一个 Map，然后转换成 JSON 字符串
        return keyword.split('+').map((part) {
          var map = {'name': part.toString(), 'wordType': 1};
          return jsonEncode(map);
        }).toList();
      } else {
        // 如果没有 "+"，直接生成一个包含一个 JSON 字符串的列表
        var map = {'name': keyword.toString(), 'wordType': 1};
        return [jsonEncode(map)];
      }
    }).toList();
  }
}
