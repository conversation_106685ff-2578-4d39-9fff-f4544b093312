import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../model/jlzs_config_model.dart';

/// 百度指数地区数据管理服务
/// 职责：地区数据加载、树形结构管理、地区选择逻辑
class BaiduAreaService extends GetxService {
  // 地区数据树形结构
  final RxList<TreeNode> _areaData = <TreeNode>[
    TreeNode("全国", 0, [])
  ].obs;
  
  // 选中的地区数据
  final RxList<TreeNode> _selectedAreaData = <TreeNode>[].obs;
  
  // 县级市列表
  static const List<String> _countyLevelCities = [
    "西双版纳", "德宏", "怒江", "迪庆", "石河子", "昌吉", "阿克苏", "博尔塔拉",
    "阿勒泰", "喀什", "和田", "巴音郭楞", "伊犁", "塔城", "五家渠", "阿拉尔",
    "图木舒克", "湘西", "巢湖", "阿拉善盟", "锡林郭勒盟", "兴安盟", "临夏",
    "甘南", "万宁", "琼海", "东方", "五指山", "文昌", "陵水", "澄迈", "乐东",
    "临高", "定安", "昌江", "屯昌", "保亭", "白沙", "琼中", "黔南", "黔东南",
    "黔西南", "海西", "玉树", "青海海南", "海北", "黄南", "果洛", "甘孜",
    "阿坝", "神农架", "潜江", "延边", "仙桃", "恩施", "红河", "济源", "莱芜",
    "文山", "天门", "楚雄", "凉山"
  ];
  
  /// 获取地区数据（只读）
  List<TreeNode> get areaData => _areaData.toList();
  
  /// 获取选中的地区数据（只读）
  List<TreeNode> get selectedAreaData => _selectedAreaData.toList();
  
  /// 获取县级市列表
  List<String> get countyLevelCities => _countyLevelCities;
  
  /// 从Assets加载JSON数据
  /// [filePath] JSON文件路径
  Future<Map<String, dynamic>> loadJsonFromAssets(String filePath) async {
    try {
      String jsonString = await rootBundle.loadString(filePath);
      return jsonDecode(jsonString);
    } catch (e) {
      throw Exception('加载JSON文件失败: $filePath, 错误: $e');
    }
  }
  
  /// 加载地区数据
  /// [type] 数据类型，如"gjc"
  Future<void> loadAreaData(String type) async {
    try {
      // 1. 加载省份和城市数据
      Map<String, dynamic> provinces = await loadJsonFromAssets('assets/provinces.json');
      provinces = provinces[type] ?? {};
      
      Map<String, dynamic> cityShip = await loadJsonFromAssets('assets/cityShip.json');
      cityShip = cityShip[type] ?? {};
      
      // 2. 清空现有数据并重新初始化
      _areaData.clear();
      _areaData.add(TreeNode("全国", 0, []));
      
      // 3. 添加省份到area_data
      for (var entry in provinces.entries) {
        _areaData[0].children.add(
          TreeNode(entry.value, int.parse(entry.key), [])
        );
      }
      
      // 4. 添加城市到相应的省份
      for (var areaDataItem in _areaData[0].children) {
        List? cityShips = cityShip[areaDataItem.id.toString()];
        
        if (cityShips != null && cityShips.isNotEmpty) {
          for (var cityShipsItem in cityShips) {
            areaDataItem.children.add(
              TreeNode(
                cityShipsItem['label'],
                int.parse(cityShipsItem['value']),
                []
              )
            );
          }
        } else {
          areaDataItem.children = [];
        }
      }
      
    } catch (e) {
      throw Exception('加载地区数据失败: $e');
    }
  }
  
  /// 将所有节点设置为未选中状态
  /// [node] 要处理的节点
  void setAllNodesUnchecked(TreeNode node) {
    node.isChecked = false;
    for (TreeNode child in node.children) {
      setAllNodesUnchecked(child);
    }
  }
  
  /// 将节点列表中的所有节点设置为未选中状态
  /// [nodeList] 节点列表
  void setAllNodesUncheckedInList(List<TreeNode> nodeList) {
    for (TreeNode node in nodeList) {
      setAllNodesUnchecked(node);
    }
  }
  
  /// 检查并标记县级市
  /// [nodes] 要检查的节点列表
  /// [dCityS] 地级市列表（从外部传入）
  Future<void> checkCountyLevelCities(List<TreeNode> nodes, List<String> dCityS) async {
    try {
      Map<String, dynamic> provinces = await loadJsonFromAssets('assets/provinces.json');
      
      for (var node in nodes) {
        // 判断当前节点是否在县级市列表中
        if (dCityS.contains(node.name)) {
          // 县级市的处理逻辑
        } else {
          node.isChecked = true;
        }
        
        // 特殊处理某些省份
        for (var entry in provinces.entries) {
          if (entry.value == node.name || node.name == "吉林" || node.name == "全国") {
            node.isChecked = false;
          }
        }
        
        // 递归处理子节点
        if (node.children.isNotEmpty) {
          await checkCountyLevelCities(node.children, dCityS);
        }
      }
    } catch (e) {
      throw Exception('检查县级市失败: $e');
    }
  }
  
  /// 手动选择城市
  /// [nodes] 要检查的节点列表
  /// [cities] 要选中的城市列表
  void checkHandMovement(List<TreeNode> nodes, List<String> cities) {
    for (var node in nodes) {
      if (cities.contains(node.name)) {
        node.isChecked = true;
      }
      
      // 递归处理子节点
      if (node.children.isNotEmpty) {
        checkHandMovement(node.children, cities);
      }
    }
  }
  
  /// 检查并标记地级市
  /// [nodes] 要检查的节点列表
  /// [dCityS] 地级市列表
  Future<void> checkPrefectureLevelCity(List<TreeNode> nodes, List<String> dCityS) async {
    for (var node in nodes) {
      if (dCityS.contains(node.name)) {
        if (node.id == 922) {
          // 特殊处理ID为922的节点
        } else {
          node.isChecked = true;
        }
      }
      
      // 递归处理子节点
      if (node.children.isNotEmpty) {
        await checkPrefectureLevelCity(node.children, dCityS);
      }
    }
  }
  
  /// 统计选中的节点数量
  /// [nodes] 要统计的节点列表
  int countCheckedNodes(List<TreeNode> nodes) {
    int count = 0;
    
    for (var node in nodes) {
      if (node.isChecked) {
        count += 1;
      }
      if (node.children.isNotEmpty) {
        count += countCheckedNodes(node.children);
      }
    }
    
    return count;
  }
  
  /// 将选中的节点扁平化为列表
  /// [nodes] 要处理的节点列表
  List<TreeNode> flattenCheckedNodes(List<TreeNode> nodes) {
    List<TreeNode> flatList = [];
    
    void traverse(TreeNode node) {
      if (node.isChecked) {
        flatList.add(node);
      }
      for (var child in node.children) {
        traverse(child);
      }
    }
    
    for (var node in nodes) {
      traverse(node);
    }
    
    return flatList;
  }
  
  /// 检查是否有选中的地区
  bool hasSelectedAreas() {
    return flattenCheckedNodes(_areaData).isNotEmpty;
  }
}
