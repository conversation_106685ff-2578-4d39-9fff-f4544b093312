import 'dart:async';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:puppeteer/puppeteer.dart';
import 'package:puppeteer/protocol/network.dart';
import 'package:http/http.dart' as http;
import '../../../model/baidu_user_model.dart';
import 'baidu_log_service.dart';

/// 百度指数账号管理服务
/// 职责：账号登录、验证、状态管理、用户选择管理
class BaiduAccountService extends GetxService {
  // 注入日志服务
  final BaiduLogService _logService = Get.find<BaiduLogService>();
  
  // 用户API密钥缓存
  String _userApiKey = '';
  String _userApiKeyTime = '';
  
  /// 账号登录
  /// [user] 要登录的用户
  Future<void> loginAccount(BaiDuUsers user) async {
    try {
      _logService.addLog('🚀 开始登录账号: ${user.username}');
      
      // 创建参数列表
      List<String> args = [];
      
      // 如果配置了代理，添加代理设置
      if (user.isProxy && user.proxyAddress != null && user.proxyPort != null) {
        args.add('--proxy-server=${user.proxyAddress}:${user.proxyPort}');
      }
      
      // 启动浏览器，直接使用 args
      var browser = await puppeteer.launch(
        headless: false,
        args: args,
      );
      
      var page = await browser.newPage();
      
      // 如果配置了代理认证，设置认证信息
      if (user.isProxy &&
          user.proxyUsername != null &&
          user.proxyUsername!.isNotEmpty &&
          user.proxyPassword != null &&
          user.proxyPassword!.isNotEmpty) {
        await page.authenticate(
          username: user.proxyUsername!,
          password: user.proxyPassword!
        );
      }
      
      await page.setRequestInterception(true);
      
      // 创建 Completer 用于等待流任务完成
      Completer<void> onRequestCompleter = Completer<void>();
      
      // 设置请求拦截
      await _setupRequestInterception(page, user, onRequestCompleter);
      
      // 设置响应处理
      await _setupResponseHandling(page, user, browser, onRequestCompleter);
      
      // 访问目标网页
      await page.goto('https://index.baidu.com/v2/main/index.html#/trend/%E5%8D%8E%E4%B8%BA?words=%E5%8D%8E%E4%B8%BA');
      
      // 获取 API 密钥
      await _extractApiKeys(page, user);
      
      // 阻塞等待 onRequest 事件完成
      await onRequestCompleter.future;
      
      _logService.addLog('✅ 账号登录完成: ${user.username}');
      
    } catch (e) {
      _logService.addLog('❌ 账号登录失败: ${user.username}, 错误: $e');
      user.isError = true;
      user.isStart = false;
      rethrow;
    }
  }
  
  /// 设置请求拦截
  Future<void> _setupRequestInterception(
    Page page, 
    BaiDuUsers user, 
    Completer<void> completer
  ) async {
    page.onRequest.listen((request) async {
      if (request.url.contains('https://dlswbr.baidu.com/heicha/mm/2057/acs-2057.js')) {
        try {
          var response = await http.get(Uri.parse(request.url));
          if (response.statusCode == 200) {
            var originalJsContent = response.body;
            
            // 定义正则表达式
            RegExp regex1 = RegExp(r"a8\(b\('([^']+)'\)\+ae\+");
            RegExp regex2 = RegExp(r"a8\('([^']+)'\s*\+\s*ae");
            
            String timeKey = "";
            String modifiedJsContent = "";
            
            // 封装匹配逻辑
            String matchDynamicValue(String content, RegExp regex) {
              Match? match = regex.firstMatch(content);
              if (match != null) {
                return match.group(1)!;
              }
              return "";
            }
            
            // 先匹配第一个字符串
            timeKey = matchDynamicValue(originalJsContent, regex1);
            
            // 如果第一个字符串没有匹配到，则匹配第二个字符串
            if (timeKey.isEmpty) {
              timeKey = matchDynamicValue(originalJsContent, regex2);
              _logService.addLog("regex2----$timeKey");
              modifiedJsContent = originalJsContent.replaceFirst(
                "function ek(){",
                '''
              window['wrrtime']= '$timeKey';
              window['wrr'] = a0;
              function ek(){
            ''',
              );
            } else {
              timeKey = "b('$timeKey')";
              modifiedJsContent = originalJsContent.replaceFirst(
                "function ek(){",
                '''
              window['wrrtime']= $timeKey;
              window['wrr'] = a0;
              function ek(){
            ''',
              );
            }
            
            // 输出结果
            if (timeKey.isNotEmpty) {
              _logService.addLog('匹配到的动态值: $timeKey');
            } else {
              _logService.addLog('未找到匹配的值');
            }
            
            await request.respond(
              status: 200,
              contentType: 'application/javascript',
              body: modifiedJsContent,
            );
          } else {
            await request.continueRequest();
          }
        } catch (e) {
          _logService.addLog('请求拦截处理失败: $e');
          await request.continueRequest();
        }
      } else {
        await request.continueRequest();
      }
    });
  }
  
  /// 设置响应处理
  Future<void> _setupResponseHandling(
    Page page, 
    BaiDuUsers user, 
    Browser browser,
    Completer<void> completer
  ) async {
    page.onResponse.listen((response) async {
      if (response.url.contains("https://index.baidu.com/api/SearchApi/index")) {
        if (response.status == 200) {
          try {
            // 获取特定 URL 的 cookies
            List<Cookie> cookies = await page.cookies(urls: ['https://index.baidu.com/api/SearchApi/index']);
            _logService.addLog('Cookies for https://index.baidu.com: $cookies');
            
            for (var cookie in cookies) {
              if (cookie.name == 'BDUSS') {
                _logService.addLog('Cookie: Name = ${cookie.name}, Value = ${cookie.value}, Domain = ${cookie.domain}, Path = ${cookie.path}');
                user.cookie = "${cookie.name}=${cookie.value};";
              }
              
              if (cookie.name == 'ab_sr') {
                user.cookie += "${cookie.name}=${cookie.value};";
              }
            }
            _logService.addLog(user.cookie);
            
            // 等待元素加载
            await page.waitForSelector('.username-text');
            var username = await page.evaluate('''() => {
             const element = document.querySelector('.username-text');
             return element ? element.innerText : null;
             }''');
            
            _logService.addLog('Username: $username');
            
            // 判断是否被封号（根据实际响应内容判断）
            await Future.delayed(Duration(milliseconds: 200));
            final body = await response.text;
            _logService.addLog(body);
            final jsonBody = jsonDecode(body) as Map<String, dynamic>;
            
            // 正确字段提取
            final status = jsonBody['status'] ?? -1;
            final data = jsonBody['data']?.toString() ?? '';
            final message = jsonBody['message']?.toString() ?? '无错误信息';
            
            _logService.addLog('状态码: $status');
            _logService.addLog('数据: $data');
            _logService.addLog('消息: $message');
            
            if (data == "" && message == "request block") {
              user.isStart = false;
              user.username = username;
              user.isError = true;
            } else {
              user.isStart = true;
              user.username = username;
              user.isError = false;
            }
            
            // 在此关闭浏览器
            await browser.close();
          } catch (e) {
            _logService.addLog('响应处理失败: $e');
          }
        } else {
          _logService.addLog("当前账户不可用");
        }
        
        // 完成后标记 Completer 完成
        if (!completer.isCompleted) completer.complete();
      }
    });
  }
  
  /// 提取API密钥
  Future<void> _extractApiKeys(Page page, BaiDuUsers user) async {
    try {
      // 获取 wrr 和 wrrtime
      var wrr = await page.evaluate('''
        function() {
          console.log("123123123213");
          return window['wrr'];
        }
      ''');
      
      var wrrtime = await page.evaluate('''
        function() {
          return window['wrrtime'];
        }
      ''');
      
      if (wrr != null && wrr != "") {
        _userApiKey = wrr;
        user.apiKey = wrr;
      } else {
        user.apiKey = _userApiKey;
      }
      
      if (wrrtime != null && wrrtime != "") {
        _userApiKeyTime = wrrtime;
        user.apiKeyTime = wrrtime.replaceAll('_', '');
      } else {
        user.apiKeyTime = _userApiKeyTime.replaceAll('_', '');
      }
      
      _logService.addLog('API Key: ${user.apiKey}');
      _logService.addLog('API Key Time: ${user.apiKeyTime}');
      
    } catch (e) {
      _logService.addLog('提取API密钥失败: $e');
    }
  }
  
  /// 检查用户是否可用
  /// [user] 要检查的用户
  bool isUserAvailable(BaiDuUsers user) {
    return user.isStart &&
        user.cookie.isNotEmpty &&
        user.apiKey.isNotEmpty;
  }
  
  /// 获取可用用户数量
  int getAvailableUserCount(List<BaiDuUsers> users) {
    return users.where((user) => isUserAvailable(user)).length;
  }
  
  /// 获取选中的用户列表
  /// [users] 用户列表
  List<BaiDuUsers> getSelectedUsers(List<BaiDuUsers> users) {
    return users.where((user) => user.isSelected).toList();
  }
  
  /// 检查是否全选
  /// [users] 用户列表
  bool isAllSelected(List<BaiDuUsers> users) {
    return users.isNotEmpty && users.every((user) => user.isSelected);
  }
  
  /// 切换全选状态
  /// [users] 用户列表
  /// [value] 选择状态
  void toggleAllSelection(List<BaiDuUsers> users, bool value) {
    for (var user in users) {
      user.isSelected = value;
    }
  }
  
  /// 切换单个用户选择状态
  /// [users] 用户列表
  /// [index] 用户索引
  /// [value] 选择状态
  void toggleUserSelection(List<BaiDuUsers> users, int index, bool value) {
    if (index >= 0 && index < users.length) {
      users[index].isSelected = value;
    }
  }
  
  /// 检查是否有可用账号
  /// [users] 用户列表
  bool hasAvailableAccount(List<BaiDuUsers> users) {
    return users.any((user) => 
      user.isStart && 
      user.username != "暂未登录" && 
      user.apiKey != null &&
      user.apiKey!.isNotEmpty
    );
  }
}
