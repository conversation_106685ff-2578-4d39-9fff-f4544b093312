import 'dart:io';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';
import 'baidu_log_service.dart';
import 'baidu_data_service.dart';

/// 百度指数工具服务
/// 职责：格式化工具、文件操作、数据处理工具、通用工具方法
class BaiduUtilService extends GetxService {
  // 注入服务
  final BaiduLogService _logService = Get.find<BaiduLogService>();
  final BaiduDataService _dataService = Get.find<BaiduDataService>();
  
  /// 格式化日期时间
  /// [dateTime] 要格式化的日期时间
  /// [format] 格式字符串，默认为 'yyyy-MM-dd HH:mm:ss'
  String formatDateTime(DateTime dateTime, [String format = 'yyyy-MM-dd HH:mm:ss']) {
    return DateFormat(format).format(dateTime);
  }
  
  /// 格式化日期
  /// [dateTime] 要格式化的日期
  /// [format] 格式字符串，默认为 'yyyy-MM-dd'
  String formatDate(DateTime dateTime, [String format = 'yyyy-MM-dd']) {
    return DateFormat(format).format(dateTime);
  }
  
  /// 将列表分割成指定大小的块
  /// [list] 要分割的列表
  /// [size] 每块的大小
  List<List<T>> partition<T>(List<T> list, int size) {
    List<List<T>> chunks = [];
    for (int i = 0; i < list.length; i += size) {
      chunks.add(list.sublist(i, i + size > list.length ? list.length : i + size));
    }
    return chunks;
  }
  
  /// 生成指定长度的索引列表
  /// [length] 列表长度
  List<int> generateIndexList(int length) {
    return List.generate(length, (index) => index);
  }
  
  /// 从列表中移除指定列
  /// [data] 二维数据列表
  /// [columnIndex] 要移除的列索引
  List<List<dynamic>> removeColumn(List<List<dynamic>> data, int columnIndex) {
    return data.map((row) {
      if (columnIndex >= 0 && columnIndex < row.length) {
        return List.from(row)..removeAt(columnIndex);
      }
      return row;
    }).toList();
  }
  
  /// 选择电子表格文件
  /// [allowedExtensions] 允许的文件扩展名
  Future<String?> pickSpreadsheetFile([List<String>? allowedExtensions]) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions ?? ['xlsx', 'xls', 'csv'],
        allowMultiple: false,
      );
      
      if (result != null && result.files.single.path != null) {
        final filePath = result.files.single.path!;
        _logService.addLog('📁 选择文件: ${result.files.single.name}');
        return filePath;
      }
      
      return null;
    } catch (e) {
      _logService.addLog('❌ 文件选择失败: $e');
      return null;
    }
  }
  
  /// 读取Excel文件
  /// [filePath] 文件路径
  Future<List<dynamic>> readExcelFile(String filePath) async {
    try {
      return await _dataService.readExcelFile(filePath);
    } catch (e) {
      _logService.addLog('❌ 读取Excel文件失败: $e');
      rethrow;
    }
  }
  
  /// 验证文件是否存在
  /// [filePath] 文件路径
  bool fileExists(String filePath) {
    return File(filePath).existsSync();
  }
  
  /// 获取文件大小（字节）
  /// [filePath] 文件路径
  int getFileSize(String filePath) {
    if (!fileExists(filePath)) return 0;
    return File(filePath).lengthSync();
  }
  
  /// 格式化文件大小
  /// [bytes] 字节数
  String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
  
  /// 延迟执行（带倒计时）
  /// [seconds] 延迟秒数
  /// [onCountdown] 倒计时回调，参数为剩余秒数
  Future<void> delayWithCountdown(int seconds, {Function(int)? onCountdown}) async {
    for (int i = seconds; i > 0; i--) {
      onCountdown?.call(i);
      await Future.delayed(const Duration(seconds: 1));
    }
  }
  
  /// 生成随机延迟时间（毫秒）
  /// [minMs] 最小延迟毫秒数
  /// [maxMs] 最大延迟毫秒数
  int generateRandomDelay(int minMs, int maxMs) {
    if (minMs >= maxMs) return minMs;
    return minMs + (DateTime.now().millisecondsSinceEpoch % (maxMs - minMs));
  }
  
  /// 随机延迟
  /// [minMs] 最小延迟毫秒数
  /// [maxMs] 最大延迟毫秒数
  Future<void> randomDelay(int minMs, int maxMs) async {
    final delayMs = generateRandomDelay(minMs, maxMs);
    await Future.delayed(Duration(milliseconds: delayMs));
  }
  
  /// 安全的字符串转整数
  /// [value] 字符串值
  /// [defaultValue] 默认值
  int safeParseInt(String? value, [int defaultValue = 0]) {
    if (value == null || value.isEmpty) return defaultValue;
    return int.tryParse(value) ?? defaultValue;
  }
  
  /// 安全的字符串转双精度浮点数
  /// [value] 字符串值
  /// [defaultValue] 默认值
  double safeParseDouble(String? value, [double defaultValue = 0.0]) {
    if (value == null || value.isEmpty) return defaultValue;
    return double.tryParse(value) ?? defaultValue;
  }
  
  /// 检查字符串是否为空或null
  /// [value] 要检查的字符串
  bool isNullOrEmpty(String? value) {
    return value == null || value.isEmpty;
  }
  
  /// 检查字符串是否不为空
  /// [value] 要检查的字符串
  bool isNotNullOrEmpty(String? value) {
    return !isNullOrEmpty(value);
  }
  
  /// 截断字符串到指定长度
  /// [text] 原始字符串
  /// [maxLength] 最大长度
  /// [suffix] 截断后的后缀，默认为 '...'
  String truncateString(String text, int maxLength, [String suffix = '...']) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - suffix.length) + suffix;
  }
  
  /// 清理字符串（移除前后空格和特殊字符）
  /// [text] 要清理的字符串
  String cleanString(String text) {
    return text.trim().replaceAll(RegExp(r'\s+'), ' ');
  }
  
  /// 验证邮箱格式
  /// [email] 邮箱地址
  bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
  
  /// 验证IP地址格式
  /// [ip] IP地址
  bool isValidIP(String ip) {
    return RegExp(r'^(\d{1,3}\.){3}\d{1,3}$').hasMatch(ip) &&
           ip.split('.').every((part) {
             final num = int.tryParse(part);
             return num != null && num >= 0 && num <= 255;
           });
  }
  
  /// 验证端口号
  /// [port] 端口号字符串
  bool isValidPort(String port) {
    final num = int.tryParse(port);
    return num != null && num > 0 && num <= 65535;
  }
  
  /// 生成唯一ID
  /// [prefix] 前缀
  String generateUniqueId([String prefix = '']) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return '$prefix$timestamp$random';
  }
  
  /// 计算两个日期之间的天数差
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  int daysBetween(DateTime startDate, DateTime endDate) {
    return endDate.difference(startDate).inDays;
  }
  
  /// 获取当前时间戳（毫秒）
  int getCurrentTimestamp() {
    return DateTime.now().millisecondsSinceEpoch;
  }
  
  /// 时间戳转日期字符串
  /// [timestamp] 时间戳（毫秒）
  /// [format] 格式字符串
  String timestampToDateString(int timestamp, [String format = 'yyyy-MM-dd HH:mm:ss']) {
    return formatDateTime(DateTime.fromMillisecondsSinceEpoch(timestamp), format);
  }
  
  /// 检查是否为今天
  /// [dateTime] 要检查的日期
  bool isToday(DateTime dateTime) {
    final now = DateTime.now();
    return dateTime.year == now.year &&
           dateTime.month == now.month &&
           dateTime.day == now.day;
  }
  
  /// 获取友好的时间描述
  /// [dateTime] 日期时间
  String getFriendlyTimeDescription(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
  
  /// 数据统计工具
  /// [numbers] 数字列表
  Map<String, double> calculateStatistics(List<num> numbers) {
    if (numbers.isEmpty) {
      return {
        'sum': 0.0,
        'average': 0.0,
        'min': 0.0,
        'max': 0.0,
        'count': 0.0,
      };
    }
    
    final sum = numbers.reduce((a, b) => a + b).toDouble();
    final average = sum / numbers.length;
    final min = numbers.reduce((a, b) => a < b ? a : b).toDouble();
    final max = numbers.reduce((a, b) => a > b ? a : b).toDouble();
    
    return {
      'sum': sum,
      'average': average,
      'min': min,
      'max': max,
      'count': numbers.length.toDouble(),
    };
  }
}
