import 'dart:async';
import 'package:get/get.dart';
import '../../../model/data_model.dart';
import '../../../model/baidu_user_model.dart';
import '../../../model/jlzs_config_model.dart';
import 'baidu_log_service.dart';
import 'baidu_api_service.dart';
import 'baidu_data_service.dart';
import 'baidu_account_service.dart';
import 'baidu_util_service.dart';

/// 任务状态枚举
enum TaskState {
  initial,    // 初始状态
  running,    // 运行中
  paused,     // 暂停
  completed,  // 完成
  error,      // 错误
  stopped,    // 停止（兼容原有代码）
}

/// 任务模型
class TaskModel {
  final String keyword;
  final String region;
  final String startDate;
  final String endDate;
  final String year;
  bool isCompleted;
  String? errorMessage;
  
  TaskModel({
    required this.keyword,
    required this.region,
    required this.startDate,
    required this.endDate,
    required this.year,
    this.isCompleted = false,
    this.errorMessage,
  });
}

/// 百度指数任务调度和执行服务
/// 职责：任务分发、执行、状态管理、并发控制
class BaiduTaskService extends GetxService {
  // 注入服务
  final BaiduLogService _logService = Get.find<BaiduLogService>();
  final BaiduApiService _apiService = Get.find<BaiduApiService>();
  final BaiduDataService _dataService = Get.find<BaiduDataService>();
  final BaiduAccountService _accountService = Get.find<BaiduAccountService>();
  final BaiduUtilService _utilService = Get.find<BaiduUtilService>();
  
  // 任务状态
  final Rx<TaskState> _taskState = TaskState.initial.obs;
  TaskState get taskState => _taskState.value;
  
  // 任务控制
  bool _shouldStop = false;
  bool _isPaused = false;
  
  // 任务统计
  int _totalTasks = 0;
  int _completedTasks = 0;
  int _failedTasks = 0;
  
  /// 获取任务进度
  double get progress {
    if (_totalTasks == 0) return 0.0;
    return _completedTasks / _totalTasks;
  }
  
  /// 获取任务统计信息
  Map<String, int> get taskStatistics => {
    'total': _totalTasks,
    'completed': _completedTasks,
    'failed': _failedTasks,
    'remaining': _totalTasks - _completedTasks - _failedTasks,
  };
  
  /// 开始百度指数任务
  /// [keywords] 关键词列表
  /// [selectedAreas] 选中的地区列表
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  /// [users] 用户列表
  /// [data] 数据模型列表
  Future<void> onBdStart({
    required List<String> keywords,
    required List<TreeNode> selectedAreas,
    required String startDate,
    required String endDate,
    required List<BaiDuUsers> users,
    required List<DataModel> data,
  }) async {
    try {
      _taskState.value = TaskState.running;
      _shouldStop = false;
      _isPaused = false;
      
      _logService.addLog('🚀 开始百度指数任务');
      _logService.addLog('关键词数量: ${keywords.length}');
      _logService.addLog('地区数量: ${selectedAreas.length}');
      _logService.addLog('时间范围: $startDate 至 $endDate');
      
      // 检查是否有可用账号
      if (!_accountService.hasAvailableAccount(users)) {
        throw Exception('没有可用的账号，请先登录账号');
      }
      
      // 分发任务给用户
      await distributeTasksToUsers(
        keywords: keywords,
        selectedAreas: selectedAreas,
        startDate: startDate,
        endDate: endDate,
        users: users,
        data: data,
      );
      
      _taskState.value = TaskState.completed;
      _logService.addLog('✅ 百度指数任务完成');
      
    } catch (e) {
      _taskState.value = TaskState.error;
      _logService.addLog('❌ 百度指数任务失败: $e');
      rethrow;
    }
  }
  
  /// 停止任务
  void onBdStop() {
    _shouldStop = true;
    _taskState.value = TaskState.initial;
    _logService.addLog('⏹️ 任务已停止');
  }
  
  /// 暂停任务
  void onBdPause() {
    _isPaused = true;
    _taskState.value = TaskState.paused;
    _logService.addLog('⏸️ 任务已暂停');
  }
  
  /// 继续任务
  void onBdContinue() {
    _isPaused = false;
    _taskState.value = TaskState.running;
    _logService.addLog('▶️ 任务已继续');
  }
  
  /// 分发任务给用户
  Future<void> distributeTasksToUsers({
    required List<String> keywords,
    required List<TreeNode> selectedAreas,
    required String startDate,
    required String endDate,
    required List<BaiDuUsers> users,
    required List<DataModel> data,
  }) async {
    // 获取可用用户
    final availableUsers = users.where((user) => 
      _accountService.isUserAvailable(user)
    ).toList();
    
    if (availableUsers.isEmpty) {
      throw Exception('没有可用的用户账号');
    }
    
    _logService.addLog('可用账号数量: ${availableUsers.length}');
    
    // 生成所有任务
    final allTasks = _generateTasks(keywords, selectedAreas, startDate, endDate);
    _totalTasks = allTasks.length;
    _completedTasks = 0;
    _failedTasks = 0;
    
    _logService.addLog('总任务数量: $_totalTasks');
    
    // 将任务分配给用户
    final taskChunks = _utilService.partition(allTasks, 
      (allTasks.length / availableUsers.length).ceil());
    
    // 并发执行任务
    final futures = <Future>[];
    for (int i = 0; i < availableUsers.length && i < taskChunks.length; i++) {
      final user = availableUsers[i];
      final tasks = taskChunks[i];
      
      futures.add(_processUserTasks(user, tasks, data));
    }
    
    // 等待所有任务完成
    await Future.wait(futures);
  }
  
  /// 生成任务列表
  List<TaskModel> _generateTasks(
    List<String> keywords,
    List<TreeNode> selectedAreas,
    String startDate,
    String endDate,
  ) {
    final tasks = <TaskModel>[];
    final years = _getYearsBetween(startDate, endDate);
    
    for (final keyword in keywords) {
      for (final area in selectedAreas) {
        for (final year in years) {
          final yearStartDate = year == startDate.substring(0, 4) 
            ? startDate 
            : '$year-01-01';
          final yearEndDate = year == endDate.substring(0, 4) 
            ? endDate 
            : '$year-12-31';
            
          tasks.add(TaskModel(
            keyword: keyword,
            region: area.name,
            startDate: yearStartDate,
            endDate: yearEndDate,
            year: year,
          ));
        }
      }
    }
    
    return tasks;
  }
  
  /// 获取日期范围内的年份列表
  List<String> _getYearsBetween(String startDate, String endDate) {
    final startYear = int.parse(startDate.substring(0, 4));
    final endYear = int.parse(endDate.substring(0, 4));
    
    return List.generate(
      endYear - startYear + 1, 
      (index) => (startYear + index).toString()
    );
  }
  
  /// 处理用户任务
  Future<void> _processUserTasks(
    BaiDuUsers user,
    List<TaskModel> tasks,
    List<DataModel> data,
  ) async {
    _logService.addLog('👤 用户 ${user.username} 开始处理 ${tasks.length} 个任务');
    
    for (final task in tasks) {
      // 检查是否需要停止或暂停
      if (_shouldStop) {
        _logService.addLog('⏹️ 用户 ${user.username} 收到停止信号');
        break;
      }
      
      while (_isPaused && !_shouldStop) {
        await Future.delayed(const Duration(seconds: 1));
      }
      
      try {
        await _processTask(user, task, data);
        task.isCompleted = true;
        _completedTasks++;
        
        _logService.addLog(
          '✅ 完成: ${task.keyword} - ${task.region} (${task.year}) '
          '[${_completedTasks}/$_totalTasks]'
        );
        
        // 随机延迟，避免请求过于频繁
        await _utilService.randomDelay(1000, 3000);
        
      } catch (e) {
        task.errorMessage = e.toString();
        _failedTasks++;
        
        _logService.addLog(
          '❌ 失败: ${task.keyword} - ${task.region} (${task.year}) - $e'
        );
        
        // 失败后稍长延迟
        await _utilService.randomDelay(2000, 5000);
      }
    }
    
    _logService.addLog('👤 用户 ${user.username} 任务处理完成');
  }
  
  /// 处理单个任务
  Future<void> _processTask(
    BaiDuUsers user,
    TaskModel task,
    List<DataModel> data,
  ) async {
    // 构建API请求参数
    final params = {
      'area': '0', // 根据实际需求调整
      'word': task.keyword,
      'startDate': task.startDate,
      'endDate': task.endDate,
    };
    
    // 发起API请求
    final response = await _apiService.searchApiRequest(
      params: params,
      user: user,
      cipherTextKeywords: [task.keyword],
    );
    
    // 获取解密密钥
    final uniqid = response['data']['uniqid'] as String;
    final ptbk = await _apiService.getPtbkApiRequest(
      uniqid: uniqid,
      user: user,
    );
    
    // 处理响应数据
    final userIndexes = response['data']['userIndexes'] as List;
    
    for (final userIndexData in userIndexes) {
      final processedData = _dataService.processSearchIndexData(
        userIndexData,
        ptbk,
        task.startDate,
        task.endDate,
      );
      
      // 将处理后的数据添加到数据模型中
      _addProcessedDataToModel(data, task, processedData);
    }
  }
  
  /// 将处理后的数据添加到数据模型
  void _addProcessedDataToModel(
    List<DataModel> data,
    TaskModel task,
    Map<String, dynamic> processedData,
  ) {
    // 查找或创建对应的数据模型
    DataModel? targetModel = data.firstWhereOrNull(
      (model) => model.region == task.region
    );
    
    if (targetModel == null) {
      targetModel = DataModel(region: task.region, data: {});
      data.add(targetModel);
    }
    
    // 确保年份数据存在
    targetModel.data ??= {};
    targetModel.data![task.year] ??= [];
    
    // 创建Drama对象并添加到数据中
    final drama = Drama(
      keyword: processedData['word'],
      all: List<int>.from(processedData['all']),
      pc: List<int>.from(processedData['pc']),
      wise: List<int>.from(processedData['wise']),
      startData: task.startDate,
      endData: task.endDate,
    );
    
    targetModel.data![task.year]!.add(drama);
  }
  
  /// 收集未完成的任务
  Map<String, Map<String, List<TaskModel>>> collectUncompletedTasks(
    List<DataModel> data,
  ) {
    final uncompletedTasks = <String, Map<String, List<TaskModel>>>{};
    
    // 这里应该根据实际的数据结构来判断哪些任务未完成
    // 暂时返回空的结果
    
    return uncompletedTasks;
  }
  
  /// 重置任务状态
  void resetTaskState() {
    _taskState.value = TaskState.initial;
    _shouldStop = false;
    _isPaused = false;
    _totalTasks = 0;
    _completedTasks = 0;
    _failedTasks = 0;
  }
}
