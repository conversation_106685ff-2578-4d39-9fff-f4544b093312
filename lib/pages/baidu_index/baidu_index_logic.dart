import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../../model/baidu_user_model.dart';
import '../../model/data_model.dart';
import '../../model/jlzs_config_model.dart';
import 'services/baidu_log_service.dart';
import 'services/baidu_area_service.dart';
import 'services/baidu_api_service.dart';
import 'services/baidu_data_service.dart';
import 'services/baidu_account_service.dart';
import 'services/baidu_proxy_service.dart';
import 'services/baidu_task_service.dart';
import 'services/baidu_util_service.dart';
import 'services/baidu_proxy_service.dart';

// 重新导出枚举和类，保持UI兼容性
export 'services/baidu_task_service.dart' show TaskState;
export 'services/baidu_proxy_service.dart' show ProxyConfig;

/// 百度指数主控制器（重构后）
/// 职责：UI状态管理、服务协调、用户交互处理
class BaiduIndexLogic extends GetxController {
  // 注入所有服务
  late final BaiduLogService _logService;
  late final BaiduAreaService _areaService;
  late final BaiduApiService _apiService;
  late final BaiduDataService _dataService;
  late final BaiduAccountService _accountService;
  late final BaiduProxyService _proxyService;
  late final BaiduTaskService _taskService;
  late final BaiduUtilService _utilService;
  
  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    _loadInitialData();
  }
  
  /// 初始化服务
  void _initializeServices() {
    _logService = Get.find<BaiduLogService>();
    _areaService = Get.find<BaiduAreaService>();
    _apiService = Get.find<BaiduApiService>();
    _dataService = Get.find<BaiduDataService>();
    _accountService = Get.find<BaiduAccountService>();
    _proxyService = Get.find<BaiduProxyService>();
    _taskService = Get.find<BaiduTaskService>();
    _utilService = Get.find<BaiduUtilService>();
    
    _logService.addLog('🚀 百度指数系统初始化完成');
  }
  
  /// 加载初始数据
  void _loadInitialData() {
    // 加载地区数据
    _areaService.loadAreaData("gjc").catchError((e) {
      _logService.addLog('❌ 地区数据加载失败: $e');
    });
  }
  
  // ==================== UI状态管理 ====================
  
  /// 关键词列表
  final RxList<String> _keyWords = <String>[].obs;
  List<String> get keyWords => _keyWords.toList();
  set keyWords(List<String> value) {
    _keyWords.clear();
    _keyWords.addAll(value);
  }

  /// 用户列表
  final RxList<BaiDuUsers> users = <BaiDuUsers>[].obs;

  /// 数据列表
  final RxList<DataModel> data = <DataModel>[].obs;
  
  /// 开始日期
  final RxString _startDate = "".obs;
  String get startDate => _startDate.value;
  set startDate(String value) => _startDate.value = value;

  /// 结束日期
  final RxString _endDate = "".obs;
  String get endDate => _endDate.value;
  set endDate(String value) => _endDate.value = value;

  /// 提取间隔
  final RxString _extractionInterval = "30".obs;
  String get extractionInterval => _extractionInterval.value;
  set extractionInterval(String value) => _extractionInterval.value = value;
  
  /// 文件存储选项
  final RxList<bool> fileStorageOptions = [false, false, true].obs;

  /// 文件存储索引
  final RxInt _fileStorageIndex = 2.obs;
  int get fileStorageIndex => _fileStorageIndex.value;
  set fileStorageIndex(int value) => _fileStorageIndex.value = value;

  /// 选择的指数类型
  final RxString _selectedIndexType = "".obs;
  String get selectedIndexType => _selectedIndexType.value;
  set selectedIndexType(String value) => _selectedIndexType.value = value;

  final RxList<String> indexTypeOptions = <String>[].obs;

  /// 地区数据（兼容原有代码）
  final RxList<TreeNode> _area_data = <TreeNode>[TreeNode("全国", 0, [])].obs;
  List<TreeNode> get area_data => _area_data.toList();
  set area_data(List<TreeNode> value) {
    _area_data.clear();
    _area_data.addAll(value);
  }

  /// 其他兼容属性
  final RxString _isAnyIndexActive = "".obs;
  String get isAnyIndexActive => _isAnyIndexActive.value;
  set isAnyIndexActive(String value) => _isAnyIndexActive.value = value;

  final RxList<String> _AnyIndexActives = <String>[].obs;
  List<String> get AnyIndexActives => _AnyIndexActives.toList();
  set AnyIndexActives(List<String> value) {
    _AnyIndexActives.clear();
    _AnyIndexActives.addAll(value);
  }

  final RxBool _r = false.obs;
  bool get r => _r.value;
  set r(bool value) => _r.value = value;

  final RxBool _z = false.obs;
  bool get z => _z.value;
  set z(bool value) => _z.value = value;

  final RxBool _y = false.obs;
  bool get y => _y.value;
  set y(bool value) => _y.value = value;

  final RxBool _n = false.obs;
  bool get n => _n.value;
  set n(bool value) => _n.value = value;

  final RxBool _sjjz = false.obs;
  bool get sjjz => _sjjz.value;
  set sjjz(bool value) => _sjjz.value = value;

  final RxBool _sjpj = false.obs;
  bool get sjpj => _sjpj.value;
  set sjpj(bool value) => _sjpj.value = value;

  /// 代理相关属性
  final RxString _pt_proxy_url = "".obs;
  String get pt_proxy_url => _pt_proxy_url.value;
  set pt_proxy_url(String value) => _pt_proxy_url.value = value;

  final RxBool _isZDProxy = false.obs;
  bool get isZDProxy => _isZDProxy.value;
  set isZDProxy(bool value) => _isZDProxy.value = value;

  /// HTTP客户端工具（兼容性）
  final httpClientUtil = Get.find<BaiduUtilService>();

  /// 文本编辑控制器
  final TextEditingController gjcTextEditingController = TextEditingController();
  
  // ==================== 服务代理方法 ====================
  
  /// 获取日志列表
  List<String> get logs => _logService.logs;
  
  /// 获取日志滚动控制器
  ScrollController get logScrollController => _logService.logScrollController;
  
  /// 获取地区数据
  List<TreeNode> get areaData => _areaService.areaData;
  
  /// 获取任务状态
  TaskState get taskState => _taskService.taskState;
  
  /// 获取任务进度
  double get taskProgress => _taskService.progress;
  
  /// 获取任务统计
  Map<String, int> get taskStatistics => _taskService.taskStatistics;
  
  // ==================== 业务方法 ====================
  
  /// 开始百度指数任务
  Future<void> onBdStart() async {
    try {
      // 验证输入
      if (!_validateInputs()) return;
      
      // 获取选中的地区
      final selectedAreas = _areaService.flattenCheckedNodes(areaData);
      if (selectedAreas.isEmpty) {
        _logService.addLog('❌ 请选择至少一个地区');
        return;
      }
      
      // 开始任务
      await _taskService.onBdStart(
        keywords: _keyWords.toList(),
        selectedAreas: selectedAreas,
        startDate: startDate,
        endDate: endDate,
        users: users.toList(),
        data: data.toList(),
      );
      
      // 更新UI
      update(['buttons', 'progress']);
      
    } catch (e) {
      _logService.addLog('❌ 任务启动失败: $e');
    }
  }
  
  /// 停止任务
  void onBdStop() {
    _taskService.onBdStop();
    update(['buttons']);
  }
  
  /// 暂停任务
  void onBdPause() {
    _taskService.onBdPause();
    update(['buttons']);
  }
  
  /// 继续任务
  void onBdContinue() {
    _taskService.onBdContinue();
    update(['buttons']);
  }
  
  /// 账号登录
  /// [user] 要登录的用户
  Future<void> loginAccount(BaiDuUsers user) async {
    try {
      await _accountService.loginAccount(user);
      update(['list']);
    } catch (e) {
      _logService.addLog('❌ 账号登录失败: $e');
    }
  }
  
  /// 检查代理
  /// [user] 用户
  Future<void> checkProxy(BaiDuUsers user) async {
    if (user.proxyAddress == null || user.proxyPort == null) {
      _logService.addLog('❌ 代理配置不完整');
      return;
    }
    
    final isValid = await _proxyService.checkProxy(
      user.proxyAddress!,
      user.proxyPort!,
      user.proxyUsername,
      user.proxyPassword,
    );
    
    if (isValid) {
      user.isProxy = true;
      _logService.addLog('✅ 代理检测成功');
    } else {
      user.isProxy = false;
      _logService.addLog('❌ 代理检测失败');
    }
    
    update(['list']);
  }
  
  /// 加载地区数据
  /// [type] 数据类型
  Future<void> loadAreaData(String type) async {
    try {
      await _areaService.loadAreaData(type);
      update(['area']);
    } catch (e) {
      _logService.addLog('❌ 地区数据加载失败: $e');
    }
  }
  
  /// 选择文件
  Future<void> pickFile() async {
    try {
      final filePath = await _utilService.pickSpreadsheetFile();
      if (filePath != null) {
        final fileData = await _utilService.readExcelFile(filePath);
        // 处理文件数据
        _processFileData(fileData);
      }
    } catch (e) {
      _logService.addLog('❌ 文件处理失败: $e');
    }
  }
  
  /// 导出CSV
  Future<void> exportCsv() async {
    try {
      if (data.isEmpty) {
        _logService.addLog('❌ 没有数据可导出');
        return;
      }
      
      // 这里应该实现具体的导出逻辑
      _logService.addLog('📊 开始导出CSV...');
      
      // 示例：导出第一个数据模型的第一个关键词
      final firstData = data.first;
      final firstKeyword = _keyWords.isNotEmpty ? _keyWords.first : '';
      
      if (firstData.region != null && firstKeyword.isNotEmpty) {
        await _dataService.exportToCSV(
          data: data.toList(),
          keyword: firstKeyword,
          region: firstData.region!,
          filePath: 'output.csv',
        );
        
        _logService.addLog('✅ CSV导出完成');
      }
      
    } catch (e) {
      _logService.addLog('❌ CSV导出失败: $e');
    }
  }
  
  /// 清空日志
  void clearLogs() {
    _logService.clearLogs();
    update(['logs']);
  }
  
  /// 添加关键词
  /// [keyword] 关键词
  void addKeyword(String keyword) {
    if (keyword.isNotEmpty && !_keyWords.contains(keyword)) {
      _keyWords.add(keyword);
      _logService.addLog('➕ 添加关键词: $keyword');
    }
  }

  /// 移除关键词
  /// [keyword] 关键词
  void removeKeyword(String keyword) {
    if (_keyWords.remove(keyword)) {
      _logService.addLog('➖ 移除关键词: $keyword');
    }
  }
  
  /// 添加用户
  /// [user] 用户
  void addUser(BaiDuUsers user) {
    users.add(user);
    _logService.addLog('👤 添加用户: ${user.username}');
    update(['list']);
  }
  
  /// 移除用户
  /// [index] 用户索引
  void removeUser(int index) {
    if (index >= 0 && index < users.length) {
      final user = users.removeAt(index);
      _logService.addLog('👤 移除用户: ${user.username}');
      update(['list']);
    }
  }

  // ==================== 兼容性方法 ====================

  /// 切换用户选择状态（兼容性方法）
  void toggleUserSelection(int index, bool value) {
    _accountService.toggleUserSelection(users.toList(), index, value);
    update(['list']);
  }

  /// 检查是否全选（兼容性方法）
  bool get isAllSelected => _accountService.isAllSelected(users.toList());

  /// 切换全选状态（兼容性方法）
  void toggleAllSelection(bool value) {
    _accountService.toggleAllSelection(users.toList(), value);
    update(['list']);
  }

  /// 获取选中的用户（兼容性方法）
  List<BaiDuUsers> getSelectedUsers() {
    return _accountService.getSelectedUsers(users.toList());
  }

  /// 统计选中的节点数量（兼容性方法）
  int countCheckedNodes(List<TreeNode> nodes) {
    return _areaService.countCheckedNodes(nodes);
  }

  /// 设置所有节点为未选中（兼容性方法）
  void setAllNodesUncheckedInList(List<TreeNode> nodes) {
    _areaService.setAllNodesUncheckedInList(nodes);
    update(['area']);
  }

  /// 检查县级市（兼容性方法）
  Future<void> checkCountyLevelCities(List<TreeNode> nodes, List<String> cities) async {
    await _areaService.checkCountyLevelCities(nodes, cities);
    update(['area']);
  }

  /// 检查地级市（兼容性方法）
  Future<void> checkPrefectureLevelCity(List<TreeNode> nodes, List<String> cities) async {
    await _areaService.checkPrefectureLevelCity(nodes, cities);
    update(['area']);
  }

  /// 手动选择城市（兼容性方法）
  void checkHandMovement(List<TreeNode> nodes, List<String> cities) {
    _areaService.checkHandMovement(nodes, cities);
    update(['area']);
  }

  /// 添加日志（兼容性方法）
  void addLog(String message) {
    _logService.addLog(message);
    update(['logs']);
  }

  /// 格式化日期时间（兼容性方法）
  String formatDateTime(DateTime dateTime, [String format = 'yyyy-MM-dd HH:mm:ss']) {
    return _utilService.formatDateTime(dateTime, format);
  }

  /// 检查退出（兼容性方法）
  void onCheckExit() {
    // 实现检查退出逻辑
    _logService.addLog('🔍 检查退出条件');
  }

  /// 数据处理方法（兼容性方法）
  void handlingData0() {
    _logService.addLog('📊 开始数据处理 (模式0)');
    // 实现具体的数据处理逻辑
  }

  /// 数据处理方法（兼容性方法）
  void handlingData2() {
    _logService.addLog('📊 开始数据处理 (模式2)');
    // 实现具体的数据处理逻辑
  }

  /// 批量导入代理（兼容性方法）
  Future<void> importProxies(List<dynamic> proxies, int validTime) async {
    try {
      // 转换代理配置格式
      final proxyConfigs = proxies.map((proxy) {
        // 这里需要根据实际的代理格式进行转换
        return proxy; // 临时返回原始数据
      }).toList();

      _logService.addLog('📥 开始批量导入代理');
      // 调用代理服务的批量导入方法
      // await _proxyService.importProxies(proxyConfigs, validTime, users.toList());
    } catch (e) {
      _logService.addLog('❌ 批量导入代理失败: $e');
    }
  }
  
  // ==================== 私有方法 ====================
  
  /// 验证输入
  bool _validateInputs() {
    if (_keyWords.isEmpty) {
      _logService.addLog('❌ 请添加至少一个关键词');
      return false;
    }

    if (startDate.isEmpty || endDate.isEmpty) {
      _logService.addLog('❌ 请选择开始和结束日期');
      return false;
    }

    if (!_accountService.hasAvailableAccount(users.toList())) {
      _logService.addLog('❌ 没有可用的账号，请先登录账号');
      return false;
    }

    return true;
  }
  
  /// 处理文件数据
  void _processFileData(List<dynamic> fileData) {
    // 假设文件第一列是关键词
    _keyWords.clear();
    for (final item in fileData) {
      if (item != null && item.toString().isNotEmpty) {
        _keyWords.add(item.toString().trim());
      }
    }

    _logService.addLog('📁 从文件导入 ${_keyWords.length} 个关键词');
    update(['keywords']);
  }
  
  @override
  void onClose() {
    _proxyService.stopProxyCheckTimer();
    super.onClose();
  }
}
