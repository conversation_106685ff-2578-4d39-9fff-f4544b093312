import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:tekflat_design/tekflat_design.dart';
import 'package:intl/intl.dart';
import 'package:bd/widgets/checkbox_group_selection.dart';
import '../baidu_index_logic.dart';
import 'baidu_index_widgets.dart';
import 'account_management_dialog.dart';

/// 配置管理区域组件
/// 包含账号管理、日期选择、指数类型选择、提取间隔设置、文件存储选项等功能
class ConfigManagementSection extends StatelessWidget {
  final BaiduIndexLogic logic;

  const ConfigManagementSection({
    Key? key,
    required this.logic,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BaiduIndexLogic>(
      id: "two",
      init: logic,
      builder: (logic) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 账号管理区域
            Container(
              width: double.infinity,
              margin: EdgeInsets.symmetric(vertical: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 账号管理按钮
                  SizedBox(
                    height: 45,
                    child: TekButton(
                      size: TekButtonSize.medium,
                      type: TekButtonType.info,
                      text: "账号管理",
                      icon: Icon(Icons.manage_accounts, size: 20),
                      onPressed: () {
                        TekDialogs.defaultDialog(
                          width: 800,
                          context,
                          content: AccountManagement(),
                        );
                      },
                    ),
                  ),
                  SizedBox(height: 12),
                  
                  // 账号状态显示
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade200),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 总数
                        CountItem(
                          count: logic.users.length,
                          label: "账号",
                          color: Colors.grey[700]!,
                        ),
                        SizedBox(width: 8),
                        // 已登录
                        CountItem(
                          count: logic.users
                              .where((user) => user.username != "暂未登录")
                              .length,
                          label: "已登录",
                          color: Colors.blue[700]!,
                        ),
                        SizedBox(width: 8),
                        // 已启用
                        CountItem(
                          count: logic.users
                              .where((user) => user.isStart)
                              .length,
                          label: "已启用",
                          color: Colors.green[700]!,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            // 日期范围选择
            TekTypography(
              text: "日期范围(注意格式)",
              type: TekTypographyType.bodyMedium,
              color: Colors.grey,
            ),
            
            // 开始日期选择
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: TekInputDateTime(
                labelText: '选择开始日期',
                hintText: '请选日期',
                enabled: true,
                filled: true,
                firstDate: () => DateTime(2000),
                fillColor: Colors.grey.shade50,
                floatingLabelBehavior: FloatingLabelBehavior.never,
                labelStyle: TextStyle(fontSize: 14),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey.shade400, width: 1),
                  borderRadius: BorderRadius.circular(2.0),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.blue, width: 1),
                  borderRadius: BorderRadius.circular(2.0),
                ),
                onChanged: (dateTime) {
                  String formattedDate = DateFormat('yyyy-MM-dd').format(dateTime!);
                  logic.startDate = formattedDate;
                  print("object----${logic.startDate}");
                  logic.update(["now", "three"]);
                },
              ),
            ),
            
            // 结束日期选择
            TekInputDateTime(
              labelText: '选择结束日期',
              hintText: '请选择结束日期',
              enabled: true,
              filled: true,
              fillColor: Colors.grey.shade50,
              firstDate: () => DateTime(2000),
              floatingLabelBehavior: FloatingLabelBehavior.never,
              labelStyle: TextStyle(fontSize: 14),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.grey.shade400, width: 1),
                borderRadius: BorderRadius.circular(2.0),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.blue, width: 1),
                borderRadius: BorderRadius.circular(2.0),
              ),
              onChanged: (dateTime) {
                String formattedDate = DateFormat('yyyy-MM-dd').format(dateTime!);
                logic.endDate = formattedDate;
                print("object----${logic.endDate}");
                logic.update(["now", "three"]);
              },
            ),
            
            // 指数类型选择
            CheckboxGroupSelection(
              onSelectionChanged: (group, selectedOptions) {
                logic.isAnyIndexActive = group;
                logic.AnyIndexActives = selectedOptions;
                print("指数模式：：：：${logic.isAnyIndexActive}");
                print(logic.AnyIndexActives);
                
                // 根据选择的指数类型加载对应的地区数据
                if (logic.isAnyIndexActive == "brand") {
                  logic.area_data.clear();
                  logic.area_data = [TreeNode("全国", 0, [])];
                  logic.loadAreaData("ppc");
                } else if (logic.isAnyIndexActive == "consult") {
                  logic.AnyIndexActives = [true, false, false];
                  logic.area_data.clear();
                  logic.area_data = [TreeNode("全国", 0, [])];
                  logic.loadAreaData("gjc");
                } else {
                  logic.area_data.clear();
                  logic.area_data = [TreeNode("全国", 0, [])];
                  logic.loadAreaData("gjc");
                }
              },
            ),
            
            // 指数选择提示
            Padding(
              padding: EdgeInsets.symmetric(vertical: 10),
              child: TekTypography(
                text: "（以上指数三选一）",
                type: TekTypographyType.bodyBold,
                color: Colors.red,
              ),
            ),
            
            // 分割线
            TekDivider(color: Colors.grey.shade400, height: 0.5),
            SizedBox(height: 12),
            
            // 提取间隔设置
            Row(
              children: [
                TekTypography(
                  text: "提取间隔(秒)：",
                  type: TekTypographyType.bodyMedium,
                  color: Colors.grey.shade600,
                ),
                Container(
                  decoration: BoxDecoration(),
                  child: TekInput(
                    width: 40,
                    maxLines: 1,
                    size: TekInputSize.areaMedium,
                    initialValue: "1",
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(vertical: 7, horizontal: 5),
                    ableSuffixIconConstraints: true,
                    textAlign: TextAlign.center,
                    textStyle: TextStyle(fontSize: 12),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(2),
                    ],
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(2.0)),
                      borderSide: BorderSide(
                        color: Colors.grey.shade400,
                        width: 0.5,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(2.0)),
                      borderSide: BorderSide(
                        color: Colors.blue,
                        width: 0.5,
                      ),
                    ),
                    onChanged: (v) {
                      logic.extractionInterval = v!;
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            
            // 功能按钮组
            Padding(
              padding: EdgeInsets.symmetric(vertical: 0),
              child: Row(
                children: [
                  Expanded(
                    child: TekButton(
                      size: TekButtonSize.small,
                      type: TekButtonType.info,
                      text: "需求图谱",
                    ),
                  ),
                  SizedBox(width: 5),
                  Expanded(
                    child: TekButton(
                      size: TekButtonSize.small,
                      type: TekButtonType.info,
                      text: "人群画像",
                    ),
                  ),
                ],
              ),
            ),
            
            // 日转周/月/年按钮
            Container(
              width: double.infinity,
              height: 40,
              margin: EdgeInsets.symmetric(vertical: 12),
              child: TekButton(
                size: TekButtonSize.small,
                type: TekButtonType.info,
                text: "日转周/月/年/均值(一键换)",
              ),
            ),
            
            // 文件存储选项
            GetBuilder<BaiduIndexLogic>(
              init: logic,
              id: "fileStorageOptions",
              builder: (logic) {
                return FileStorageOptions(
                  labels: [
                    "全部放在一个文件里",
                    "每个关键词分开存放",
                    "关键词和地区都分开存放",
                  ],
                  selectedIndex: logic.fileStorageIndex,
                  onChanged: (index) {
                    logic.fileStorageIndex = index;
                    print(logic.fileStorageIndex);
                    logic.update(['fileStorageOptions']);
                  },
                );
              },
            ),
          ],
        );
      },
    );
  }
}
